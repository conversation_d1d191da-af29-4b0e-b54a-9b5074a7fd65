name: "代码问题反馈"
description: "此模板仅用于反馈代码相关问题，例如出现编译错误、运行报错、逻辑缺陷。"
title: "[Code Issue]: "
labels: ["bug", "code issue"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        **⚠ 注意：此处仅受理代码本身的问题，包括但不限于编译错误、运行报错、逻辑异常等。**  
        **如果是接口配置或环境部署等问题，请自行阅读文档或在讨论区寻求帮助。**  
        **如果是意见或建议，请使用 [意见模板](?template=opinion.yml)。**  
        感谢你的配合！

  - type: textarea
    id: description
    attributes:
      label: "问题描述"
      description: "请清晰、简要地描述代码出现的问题。"
      placeholder: "例如：运行时报错xxx，或逻辑存在xxx。"
    validations:
      required: true

  - type: textarea
    id: steps
    attributes:
      label: "复现步骤"
      description: "请提供完整的复现步骤，以便我们定位和解决问题。"
      placeholder: |
        1. ...
        2. ...
        3. ...
    validations:
      required: true

  - type: input
    id: environment
    attributes:
      label: "环境信息"
      description: "如编译器、操作系统、依赖版本等。"
      placeholder: "示例：Windows 10, Node.js v14, Python 3.9, etc."

  - type: textarea
    id: logs
    attributes:
      label: "日志信息（如适用）"
      description: "如果有报错日志或截图，可以贴在此处。"
      placeholder: "请粘贴日志内容或相关截图链接（可选）"
    validations:
      required: false

  - type: textarea
    id: additional
    attributes:
      label: "补充信息"
      description: "如果有更多信息，可在此补充。"
      placeholder: "任何与问题相关的额外背景说明..."
    validations:
      required: false
